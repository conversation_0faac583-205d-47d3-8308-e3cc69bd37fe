# Deployment Secrets Configuration

This document outlines the required GitHub secrets for the updated deployment workflows.

## Overview

The deployment workflows have been updated to use environment-specific secrets for better security and separation between staging and production environments.

## Required GitHub Secrets

### Staging Environment Secrets
Configure these secrets in your GitHub repository settings for the staging deployment:

- `STAGING_SSH_PRIVATE_KEY` - SSH private key for accessing the staging server
- `STAGING_EC2_HOST` - Hostname or IP address of the staging server
- `STAGING_SSH_USER` - SSH username for the staging server (e.g., `ubuntu`, `ec2-user`)

### Production Environment Secrets
Configure these secrets in your GitHub repository settings for the production deployment:

- `PROD_SSH_PRIVATE_KEY` - SSH private key for accessing the production server
- `PROD_EC2_HOST` - Hostname or IP address of the production server
- `PROD_SSH_USER` - SSH username for the production server (e.g., `ubuntu`, `ec2-user`)

## Workflow Changes Summary

### Staging Workflow (`deploy-staging.yml`)
- **Trigger**: Pushes to `development` branch
- **Target**: Staging environment (`dev.nabih.sa`)
- **Directory**: `/var/www/nabih-staging`
- **Docker Compose**: Uses `docker-compose.yml` (development configuration)
- **Dependencies**: Installs with dev dependencies for debugging
- **Cache Strategy**: Less aggressive caching (clears cache instead of optimizing)
- **Container**: Targets `nabih-app-dev` container

### Production Workflow (`deploy-ec2-optimized.yml`)
- **Trigger**: Pushes to `main` branch
- **Target**: Production environment (`api.nabih.sa`)
- **Directory**: `/var/www/nabih-prod`
- **Docker Compose**: Uses `docker-compose.prod.optimized.yml` (production configuration)
- **Dependencies**: Installs without dev dependencies, with optimization flags
- **Cache Strategy**: Aggressive optimization and caching
- **Container**: Targets `nabih-app` container

## Key Differences Between Environments

### Staging Environment Features:
1. **Development Dependencies**: Includes dev dependencies for debugging
2. **Less Aggressive Caching**: Uses cache clearing instead of optimization
3. **Longer Initialization**: 15-second wait for container initialization
4. **Development Docker Config**: Uses standard docker-compose.yml
5. **Staging Directory**: Deploys to `/var/www/nabih-staging`

### Production Environment Features:
1. **Production Dependencies**: No dev dependencies, optimized autoloader
2. **Aggressive Optimization**: Full Laravel optimization and caching
3. **Faster Initialization**: 10-second wait for container initialization
4. **Production Docker Config**: Uses optimized production docker-compose file
5. **Production Directory**: Deploys to `/var/www/nabih-prod`

## Migration Steps

1. **Update GitHub Secrets**: Add the new environment-specific secrets listed above
2. **Server Setup**: Ensure both staging and production servers have the correct directory structure
3. **Test Deployments**: Test both staging and production deployments to ensure they work correctly

## Security Benefits

- **Environment Isolation**: Separate SSH keys and access credentials for each environment
- **Reduced Risk**: Compromised staging credentials don't affect production
- **Better Auditing**: Clear separation of deployment activities by environment
- **Principle of Least Privilege**: Each environment only has access to its own resources

## Troubleshooting

If deployments fail after these changes:

1. **Check Secrets**: Ensure all required secrets are properly configured in GitHub
2. **Verify SSH Keys**: Confirm SSH keys have proper permissions on target servers
3. **Directory Structure**: Ensure target directories exist on servers
4. **Docker Compose Files**: Verify the correct docker-compose files exist in the repository
5. **Container Names**: Check that container names match between docker-compose files and deployment scripts

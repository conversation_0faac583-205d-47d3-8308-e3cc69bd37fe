# Deployment Secrets Configuration

This document outlines the required GitHub secrets for the updated deployment workflows.

## Overview

The deployment workflows have been updated to use environment-specific secrets for better security and separation between staging and production environments.

## Required GitHub Secrets

### Staging Environment Secrets
Configure these secrets in your GitHub repository settings for the staging deployment:

- `STAGING_SSH_PRIVATE_KEY` - SSH private key for accessing the staging server
- `STAGING_EC2_HOST` - Hostname or IP address of the staging server
- `STAGING_SSH_USER` - SSH username for the staging server (e.g., `ubuntu`, `ec2-user`)

### Production Environment Secrets
Configure these secrets in your GitHub repository settings for the production deployment:

- `PROD_SSH_PRIVATE_KEY` - SSH private key for accessing the production server
- `PROD_EC2_HOST` - Hostname or IP address of the production server
- `PROD_SSH_USER` - SSH username for the production server (e.g., `ubuntu`, `ec2-user`)

## Optional GitHub Variables

### Staging Environment Variables
Configure these variables in your GitHub repository settings to customize staging deployment:

- `STAGING_APP_DIR` - Application directory on staging server (default: `/var/www/nabih-staging`)
- `STAGING_HOST` - Staging domain/hostname (default: `dev.nabih.sa`)
- `STAGING_BRANCH` - Git branch to deploy (default: `development`)
- `STAGING_DOCKER_COMPOSE_FILE` - Docker compose file to use (default: `docker-compose.yml`)
- `STAGING_CONTAINER_NAME` - Main container name (default: `nabih-app-dev`)
- `STAGING_LETSENCRYPT_EMAIL` - Email for SSL certificates (default: `<EMAIL>`)
- `STAGING_COMPOSER_FLAGS` - Composer install flags (default: `--optimize-autoloader --no-interaction --prefer-dist`)
- `STAGING_INIT_WAIT_TIME` - Container initialization wait time in seconds (default: `15`)

### Production Environment Variables
Configure these variables in your GitHub repository settings to customize production deployment:

- `PROD_APP_DIR` - Application directory on production server (default: `/var/www/nabih-prod`)
- `PROD_HOST` - Production domain/hostname (default: `api.nabih.sa`)
- `PROD_BRANCH` - Git branch to deploy (default: `main`)
- `PROD_DOCKER_COMPOSE_FILE` - Docker compose file to use (default: `docker-compose.prod.optimized.yml`)
- `PROD_CONTAINER_NAME` - Main container name (default: `nabih-app`)
- `PROD_LETSENCRYPT_EMAIL` - Email for SSL certificates (default: `<EMAIL>`)
- `PROD_COMPOSER_FLAGS` - Composer install flags (default: `--no-dev --optimize-autoloader --no-interaction --prefer-dist --classmap-authoritative --ignore-platform-req=ext-grpc`)

## Workflow Changes Summary

### Staging Workflow (`deploy-staging.yml`)
- **Trigger**: Pushes to `development` branch (or configured branch)
- **Target**: Staging environment (configurable via `STAGING_HOST`)
- **Directory**: Configurable via `STAGING_APP_DIR` (default: `/var/www/nabih-staging`)
- **Docker Compose**: Configurable via `STAGING_DOCKER_COMPOSE_FILE` (default: `docker-compose.yml`)
- **Dependencies**: Configurable via `STAGING_COMPOSER_FLAGS` (includes dev dependencies by default)
- **Cache Strategy**: Less aggressive caching (clears cache instead of optimizing)
- **Container**: Configurable via `STAGING_CONTAINER_NAME` (default: `nabih-app-dev`)
- **Initialization**: Configurable wait time via `STAGING_INIT_WAIT_TIME` (default: 15 seconds)

### Production Workflow (`deploy-production.yml`)
- **Trigger**: Pushes to `main` branch (or configured branch)
- **Target**: Production environment (configurable via `PROD_HOST`)
- **Directory**: Configurable via `PROD_APP_DIR` (default: `/var/www/nabih-prod`)
- **Docker Compose**: Configurable via `PROD_DOCKER_COMPOSE_FILE` (default: `docker-compose.prod.optimized.yml`)
- **Dependencies**: Configurable via `PROD_COMPOSER_FLAGS` (production-optimized by default)
- **Cache Strategy**: Aggressive optimization and caching
- **Container**: Configurable via `PROD_CONTAINER_NAME` (default: `nabih-app`)

## Key Differences Between Environments

### Staging Environment Features:
1. **Development Dependencies**: Includes dev dependencies for debugging
2. **Less Aggressive Caching**: Uses cache clearing instead of optimization
3. **Longer Initialization**: 15-second wait for container initialization
4. **Development Docker Config**: Uses standard docker-compose.yml
5. **Staging Directory**: Deploys to `/var/www/nabih-staging`

### Production Environment Features:
1. **Production Dependencies**: No dev dependencies, optimized autoloader
2. **Aggressive Optimization**: Full Laravel optimization and caching
3. **Faster Initialization**: 10-second wait for container initialization
4. **Production Docker Config**: Uses optimized production docker-compose file
5. **Production Directory**: Deploys to `/var/www/nabih-prod`

## Configuration Steps

### 1. Configure GitHub Secrets (Required)
In your GitHub repository, go to Settings > Secrets and variables > Actions:

1. **Add Secrets**: Configure all required secrets listed above
2. **Verify Access**: Ensure the SSH keys have proper permissions on target servers

### 2. Configure GitHub Variables (Optional)
In your GitHub repository, go to Settings > Secrets and variables > Actions > Variables tab:

1. **Add Variables**: Configure any variables you want to customize from the lists above
2. **Leave Defaults**: Variables not configured will use the default values shown in parentheses
3. **Environment-Specific**: Use the appropriate prefix (`STAGING_` or `PROD_`) for each environment

### 3. Server Setup
1. **Directory Structure**: Ensure both staging and production servers have the correct directory structure
2. **Docker Setup**: Verify Docker and Docker Compose are installed on target servers
3. **Git Repository**: Ensure the application repository is cloned in the specified directories

### 4. Test Deployments
1. **Staging First**: Test staging deployment to verify configuration
2. **Production**: Test production deployment after staging is working
3. **Monitor Logs**: Check GitHub Actions logs for any configuration issues

## Security Benefits

- **Environment Isolation**: Separate SSH keys and access credentials for each environment
- **Reduced Risk**: Compromised staging credentials don't affect production
- **Better Auditing**: Clear separation of deployment activities by environment
- **Principle of Least Privilege**: Each environment only has access to its own resources

## Troubleshooting

If deployments fail after these changes:

1. **Check Secrets**: Ensure all required secrets are properly configured in GitHub
2. **Verify SSH Keys**: Confirm SSH keys have proper permissions on target servers
3. **Directory Structure**: Ensure target directories exist on servers
4. **Docker Compose Files**: Verify the correct docker-compose files exist in the repository
5. **Container Names**: Check that container names match between docker-compose files and deployment scripts

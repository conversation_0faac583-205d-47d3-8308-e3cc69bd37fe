#!/bin/bash

# SSL Certificate Setup for Nabih API
# This script helps set up SSL certificates for both staging and production

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local message=$1
    local color=$2
    echo -e "${color}${message}${NC}"
}

# Configuration - Use environment variables or defaults
STAGING_DOMAIN="${STAGING_HOST:-dev.nabih.sa}"
PRODUCTION_DOMAIN="${PROD_HOST:-api.nabih.sa}"
EMAIL="${LETSENCRYPT_EMAIL:-<EMAIL>}"
DOCKER_COMPOSE_FILE="${DOCKER_COMPOSE_FILE:-docker-compose.prod.optimized.yml}"

print_usage() {
    print_message "SSL Certificate Setup for Nabih API" "${BLUE}"
    echo "Usage: $0 [staging|production|both|renew|status]"
    echo ""
    echo "Commands:"
    echo "  staging     Generate SSL for staging ($STAGING_DOMAIN)"
    echo "  production  Generate SSL for production ($PRODUCTION_DOMAIN)"
    echo "  both        Generate SSL for both environments"
    echo "  renew       Renew existing certificates"
    echo "  status      Check certificate status"
    echo ""
    echo "Environment Variables:"
    echo "  STAGING_HOST        Staging domain (default: dev.nabih.sa)"
    echo "  PROD_HOST          Production domain (default: api.nabih.sa)"
    echo "  LETSENCRYPT_EMAIL  Email for certificates (default: <EMAIL>)"
    echo "  DOCKER_COMPOSE_FILE Docker compose file (default: docker-compose.prod.optimized.yml)"
    echo ""
}

setup_ssl_staging() {
    print_message "Setting up SSL certificate for STAGING: $STAGING_DOMAIN" "${YELLOW}"

    # Make sure we have the certbot directories
    mkdir -p ./certbot/{conf,www}

    # Set environment variables
    export NGINX_HOST="$STAGING_DOMAIN"
    export LETSENCRYPT_EMAIL="$EMAIL"
    export DB_HOST="mysql"  # Staging uses local MySQL

    # Start nginx first
    print_message "Starting nginx container..." "${BLUE}"
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d nginx
    sleep 10

    # Check if nginx is running
    if ! docker compose -f "$DOCKER_COMPOSE_FILE" ps nginx | grep -q "Up"; then
        print_message "Failed to start nginx container" "${RED}"
        return 1
    fi

    # Generate certificate
    print_message "Obtaining certificate for $STAGING_DOMAIN..." "${BLUE}"
    docker compose -f "$DOCKER_COMPOSE_FILE" run --rm certbot certonly \
        --webroot \
        -w /var/www/certbot \
        --email "$EMAIL" \
        -d "$STAGING_DOMAIN" \
        --agree-tos \
        --non-interactive \
        --force-renewal

    if [ $? -eq 0 ]; then
        print_message "SSL certificate for $STAGING_DOMAIN generated successfully!" "${GREEN}"
        # Reload nginx to use the new certificate
        docker compose -f "$DOCKER_COMPOSE_FILE" exec nginx nginx -s reload || \
        docker compose -f "$DOCKER_COMPOSE_FILE" restart nginx
        print_message "Nginx reloaded with new certificate" "${GREEN}"
    else
        print_message "Failed to generate SSL certificate for $STAGING_DOMAIN" "${RED}"
        return 1
    fi
}

setup_ssl_production() {
    print_message "Setting up SSL certificate for PRODUCTION: $PRODUCTION_DOMAIN" "${YELLOW}"

    # Make sure we have the certbot directories
    mkdir -p ./certbot/{conf,www}

    # Set environment variables
    export NGINX_HOST="$PRODUCTION_DOMAIN"
    export LETSENCRYPT_EMAIL="$EMAIL"
    export DB_HOST="database-1.c700o0q8ygwy.me-south-1.rds.amazonaws.com"  # Production uses RDS

    # Start nginx first
    print_message "Starting nginx container..." "${BLUE}"
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d nginx
    sleep 10

    # Check if nginx is running
    if ! docker compose -f "$DOCKER_COMPOSE_FILE" ps nginx | grep -q "Up"; then
        print_message "Failed to start nginx container" "${RED}"
        return 1
    fi

    # Generate certificate
    print_message "Obtaining certificate for $PRODUCTION_DOMAIN..." "${BLUE}"
    docker compose -f "$DOCKER_COMPOSE_FILE" run --rm certbot certonly \
        --webroot \
        -w /var/www/certbot \
        --email "$EMAIL" \
        -d "$PRODUCTION_DOMAIN" \
        --agree-tos \
        --non-interactive \
        --force-renewal

    if [ $? -eq 0 ]; then
        print_message "SSL certificate for $PRODUCTION_DOMAIN generated successfully!" "${GREEN}"
        # Reload nginx to use the new certificate
        docker compose -f "$DOCKER_COMPOSE_FILE" exec nginx nginx -s reload || \
        docker compose -f "$DOCKER_COMPOSE_FILE" restart nginx
        print_message "Nginx reloaded with new certificate" "${GREEN}"
    else
        print_message "Failed to generate SSL certificate for $PRODUCTION_DOMAIN" "${RED}"
        return 1
    fi
}

renew_certificates() {
    print_message "Renewing SSL certificates..." "${YELLOW}"

    # Make sure certbot directories exist
    mkdir -p ./certbot/{conf,www}

    # Start nginx if not running
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d nginx
    sleep 5

    # Renew certificates
    print_message "Running certificate renewal..." "${BLUE}"
    docker compose -f "$DOCKER_COMPOSE_FILE" run --rm certbot renew \
        --webroot \
        -w /var/www/certbot \
        --quiet

    if [ $? -eq 0 ]; then
        print_message "Certificate renewal completed successfully!" "${GREEN}"
        # Reload nginx
        docker compose -f "$DOCKER_COMPOSE_FILE" exec nginx nginx -s reload || \
        docker compose -f "$DOCKER_COMPOSE_FILE" restart nginx
        print_message "Nginx reloaded" "${GREEN}"
    else
        print_message "Certificate renewal failed or no renewal needed" "${YELLOW}"
    fi
}

check_certificate_status() {
    print_message "Checking SSL certificate status..." "${BLUE}"

    local domains=("$STAGING_DOMAIN" "$PRODUCTION_DOMAIN")

    for domain in "${domains[@]}"; do
        local cert_path="./certbot/conf/live/$domain/fullchain.pem"

        if [ -f "$cert_path" ]; then
            print_message "Certificate found for $domain:" "${GREEN}"

            # Check expiry date
            if command -v openssl > /dev/null; then
                expiry_date=$(openssl x509 -enddate -noout -in "$cert_path" | cut -d= -f2)
                expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
                current_timestamp=$(date +%s)
                days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))

                echo "  Expires: $expiry_date"
                if [ $days_until_expiry -lt 30 ]; then
                    print_message "  WARNING: Certificate expires in $days_until_expiry days!" "${RED}"
                else
                    print_message "  Valid for $days_until_expiry more days" "${GREEN}"
                fi
            fi
            echo ""
        else
            print_message "No certificate found for $domain" "${RED}"
            echo ""
        fi
    done
}

case $1 in
    staging)
        setup_ssl_staging
        ;;
    production)
        setup_ssl_production
        ;;
    both)
        setup_ssl_staging
        setup_ssl_production
        ;;
    renew)
        renew_certificates
        ;;
    status)
        check_certificate_status
        ;;
    *)
        print_usage
        exit 1
        ;;
esac

print_message "SSL operation completed!" "${GREEN}"

#!/bin/bash

# SSL Certificate Setup for Nabih API
# This script helps set up SSL certificates for both staging and production

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local message=$1
    local color=$2
    echo -e "${color}${message}${NC}"
}

# Configuration
STAGING_DOMAIN="dev.api.nabih.sa"
PRODUCTION_DOMAIN="api.nabih.sa"
EMAIL="<EMAIL>"

print_usage() {
    print_message "SSL Certificate Setup for Nabih API" "${BLUE}"
    echo "Usage: $0 [staging|production|both]"
    echo ""
    echo "Commands:"
    echo "  staging     Generate SSL for staging (dev.api.nabih.sa)"
    echo "  production  Generate SSL for production (api.nabih.sa)"  
    echo "  both        Generate SSL for both environments"
    echo ""
}

setup_ssl_staging() {
    print_message "Setting up SSL certificate for STAGING: $STAGING_DOMAIN" "${YELLOW}"
    
    # Make sure we have the certbot directories
    mkdir -p ./certbot/{conf,www}
    
    # Set environment variables
    export NGINX_HOST="$STAGING_DOMAIN"
    export LETSENCRYPT_EMAIL="$EMAIL"
    
    # Start nginx first
    docker compose -f docker-compose.prod.optimized.yml up -d nginx
    sleep 5
    
    # Generate certificate
    print_message "Obtaining certificate for $STAGING_DOMAIN..." "${BLUE}"
    docker compose -f docker-compose.prod.optimized.yml run --rm certbot certonly \
        --webroot \
        -w /var/www/certbot \
        --email "$EMAIL" \
        -d "$STAGING_DOMAIN" \
        --agree-tos \
        --non-interactive
    
    if [ $? -eq 0 ]; then
        print_message "SSL certificate for $STAGING_DOMAIN generated successfully!" "${GREEN}"
        docker compose -f docker-compose.prod.optimized.yml exec nginx nginx -s reload
    else
        print_message "Failed to generate SSL certificate for $STAGING_DOMAIN" "${RED}"
        return 1
    fi
}

setup_ssl_production() {
    print_message "Setting up SSL certificate for PRODUCTION: $PRODUCTION_DOMAIN" "${YELLOW}"
    
    # Make sure we have the certbot directories
    mkdir -p ./certbot/{conf,www}
    
    # Set environment variables
    export NGINX_HOST="$PRODUCTION_DOMAIN"
    export LETSENCRYPT_EMAIL="$EMAIL"
    
    # Start nginx first
    docker compose -f docker-compose.prod.optimized.yml up -d nginx
    sleep 5
    
    # Generate certificate
    print_message "Obtaining certificate for $PRODUCTION_DOMAIN..." "${BLUE}"
    docker compose -f docker-compose.prod.optimized.yml run --rm certbot certonly \
        --webroot \
        -w /var/www/certbot \
        --email "$EMAIL" \
        -d "$PRODUCTION_DOMAIN" \
        --agree-tos \
        --non-interactive
    
    if [ $? -eq 0 ]; then
        print_message "SSL certificate for $PRODUCTION_DOMAIN generated successfully!" "${GREEN}"
        docker compose -f docker-compose.prod.optimized.yml exec nginx nginx -s reload
    else
        print_message "Failed to generate SSL certificate for $PRODUCTION_DOMAIN" "${RED}"
        return 1
    fi
}

case $1 in
    staging)
        setup_ssl_staging
        ;;
    production)
        setup_ssl_production
        ;;
    both)
        setup_ssl_staging
        setup_ssl_production
        ;;
    *)
        print_usage
        exit 1
        ;;
esac

print_message "SSL setup completed!" "${GREEN}"

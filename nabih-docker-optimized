#!/bin/bash

set -e

# Auto-detect environment based on current directory
CURRENT_DIR=$(pwd)
if [[ "$CURRENT_DIR" == *"nabih-prod"* ]]; then
    PROJECT_NAME="nabih-prod"
    DEFAULT_DOMAIN="api.nabih.sa"
    ENV_TYPE="production"
elif [[ "$CURRENT_DIR" == *"nabih-api"* ]]; then
    PROJECT_NAME="nabih-api"
    DEFAULT_DOMAIN="dev.nabih.sa"
    ENV_TYPE="staging"
else
    # Fallback to staging if unsure
    PROJECT_NAME="nabih-api"
    DEFAULT_DOMAIN="dev.nabih.sa"
    ENV_TYPE="staging"
fi

DEV_COMPOSE_FILE="docker-compose.yml"
PROD_COMPOSE_FILE="docker-compose.prod.optimized.yml"
ENV="dev"  # Default to development environment

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_message() {
    local message=$1
    local color=$2
    echo -e "${color}${message}${NC}"
}

print_usage() {
    print_message "Docker Management Script for Nabih ($ENV_TYPE environment)" "${CYAN}"
    echo "Usage: $0 [COMMAND] [dev|prod]"
    echo ""
    echo "Commands:"
    echo "  up [dev|prod]           Start services (default: dev)"
    echo "  down [dev|prod]         Stop services (default: dev)"
    echo "  build [dev|prod]        Build services (default: dev)"
    echo "  rebuild [dev|prod]      Rebuild services from scratch (default: dev)"
    echo "  logs [dev|prod] [service] View logs (default: dev, all services)"
    echo "  shell [dev|prod]        Access app container shell (default: dev)"
    echo "  mysql [dev|prod]        Access MySQL container (default: dev)"
    echo "  redis [dev|prod]        Access Redis container (default: dev)"
    echo "  artisan [command]       Run artisan command in app container"
    echo "  composer [command]      Run composer command in app container"
    echo "  npm [command]           Run npm command in app container"
    echo "  migrate [dev|prod]      Run database migrations (default: dev)"
    echo "  seed [dev|prod]         Run database seeders (default: dev)"
    echo "  fresh [dev|prod]        Fresh migrate with seed (default: dev)"
    echo "  optimize [dev|prod]     Optimize Laravel application (default: dev)"
    echo "  ssl [domain]            Generate SSL certificate (production only)"
    echo "  status [dev|prod]       Show container status (default: dev)"
    echo "  clean                   Clean up Docker system"
    echo "  install                 Full installation setup"
    echo ""
    echo "Examples:"
    echo "  $0 up prod              Start production services"
    echo "  $0 fresh prod           Fresh migrate with prod environment"
    echo "  $0 artisan migrate      Run artisan migrate command"
    echo "  $0 logs prod app        View app container logs in prod"
    echo ""
    echo "Environment Variables:"
    echo "  NGINX_HOST              Domain name (default: $DEFAULT_DOMAIN)"
    echo "  LETSENCRYPT_EMAIL       Email for Let's Encrypt (default: <EMAIL>)"
    echo ""
    echo "Current environment: $ENV_TYPE ($PROJECT_NAME)"
    echo "Default domain: $DEFAULT_DOMAIN"
}

# Parse environment argument - handle both positions
parse_env() {
    local arg1=$1
    local arg2=$2
    
    # Check if first argument is environment
    case $arg1 in
        dev|development)
            ENV="dev"
            return 0
            ;;
        prod|production)
            ENV="prod"
            return 0
            ;;
    esac
    
    # Check if second argument is environment
    case $arg2 in
        dev|development)
            ENV="dev"
            return 0
            ;;
        prod|production)
            ENV="prod"
            return 0
            ;;
    esac
    
    # No valid environment found, keep default
    if [ -n "$arg1" ] && [[ ! "$arg1" =~ ^(up|down|build|rebuild|logs|shell|mysql|redis|artisan|composer|npm|migrate|seed|fresh|optimize|ssl|status|clean|install)$ ]]; then
        print_message "Unknown environment: $arg1. Using default: $ENV" "${YELLOW}"
    fi
}

get_compose_file() {
    if [ "$ENV" = "prod" ]; then
        echo $PROD_COMPOSE_FILE
    else
        echo $DEV_COMPOSE_FILE
    fi
}

# Check if required files exist
check_prerequisites() {
    local compose_file=$(get_compose_file)
    
    if [ ! -f "$compose_file" ]; then
        print_message "Error: $compose_file not found" "${RED}"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_message "Error: Docker is not installed" "${RED}"
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        print_message "Error: Docker Compose is not installed" "${RED}"
        exit 1
    fi
}

# Docker Compose wrapper
dc() {
    local compose_file=$(get_compose_file)
    docker compose -f "$compose_file" "$@"
}

# Service management
start_services() {
    parse_env $1 $2
    local compose_file=$(get_compose_file)
    
    print_message "Starting $PROJECT_NAME services ($ENV environment)..." "${GREEN}"
    
    # Set environment variables
    export NGINX_HOST="${NGINX_HOST:-$DEFAULT_DOMAIN}"
    export LETSENCRYPT_EMAIL="${LETSENCRYPT_EMAIL:-<EMAIL>}"
    
    if [ "$ENV" = "prod" ]; then
        print_message "Production environment detected - using optimized configuration" "${BLUE}"
        print_message "Domain: $NGINX_HOST" "${CYAN}"
        
        # Create necessary directories for production
        mkdir -p ./storage/app/public
        mkdir -p ./storage/framework/{cache,sessions,views}
        mkdir -p ./storage/logs
        mkdir -p ./certbot/{conf,www}
        
        # Set proper permissions
        chmod -R 755 ./storage
        chmod -R 755 ./bootstrap/cache
        
        dc up -d
        
        print_message "Waiting for containers to initialize..." "${YELLOW}"
        sleep 5
        
        # Check if we need to install dependencies
        if [ ! -d "./vendor" ] || [ ! -f "./vendor/autoload.php" ]; then
            print_message "Installing Composer dependencies..." "${BLUE}"
            dc exec -T app composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist --classmap-authoritative --ignore-platform-req=ext-grpc
        fi
        
        if [ ! -d "./node_modules" ] || [ ! -f "./public/build/manifest.json" ]; then
            print_message "Installing NPM dependencies and building assets..." "${BLUE}"
            dc exec -T app npm install
            dc exec -T app npm run build
        fi
        
        print_message "Running database migrations..." "${BLUE}"
        dc exec -T app php artisan migrate --force
        
        print_message "Optimizing Laravel application..." "${BLUE}"
        dc exec -T app php artisan optimize:clear
        dc exec -T app php artisan optimize
        
        print_message "Production services started successfully!" "${GREEN}"
        print_message "Application URL: https://$NGINX_HOST" "${CYAN}"
        print_message "PhpMyAdmin: http://$NGINX_HOST:8080" "${CYAN}"
    else
        dc up -d
        print_message "Development services started successfully!" "${GREEN}"
        print_message "Application URL: http://localhost:8000" "${CYAN}"
        print_message "PhpMyAdmin: http://localhost:3307" "${CYAN}"
        print_message "Mailpit: http://localhost:8025" "${CYAN}"
    fi
}

stop_services() {
    parse_env $1 $2
    local compose_file=$(get_compose_file)
    
    print_message "Stopping $PROJECT_NAME services ($ENV environment)..." "${YELLOW}"
    dc down
    print_message "Services stopped successfully!" "${GREEN}"
}

build_services() {
    parse_env $1 $2
    
    print_message "Building $PROJECT_NAME services ($ENV environment)..." "${BLUE}"
    dc build
    print_message "Build completed successfully!" "${GREEN}"
}

rebuild_services() {
    parse_env $1 $2
    
    print_message "Rebuilding $PROJECT_NAME services from scratch ($ENV environment)..." "${BLUE}"
    dc down
    dc build --no-cache
    print_message "Rebuild completed successfully!" "${GREEN}"
    
    print_message "Starting services..." "${BLUE}"
    start_services $1 $2
}

view_logs() {
    parse_env $1 $2
    local service=""
    
    # Determine which argument is the service name
    if [[ ! "$1" =~ ^(dev|development|prod|production)$ ]]; then
        service=$1
    elif [[ ! "$2" =~ ^(dev|development|prod|production)$ ]] && [ -n "$2" ]; then
        service=$2
    elif [ -n "$3" ]; then
        service=$3
    fi
    
    if [ -n "$service" ]; then
        print_message "Viewing logs for $service ($ENV environment)..." "${BLUE}"
        dc logs -f "$service"
    else
        print_message "Viewing all logs ($ENV environment)..." "${BLUE}"
        dc logs -f
    fi
}

access_shell() {
    parse_env $1 $2
    
    print_message "Accessing app container shell ($ENV environment)..." "${BLUE}"
    dc exec app bash
}

access_mysql() {
    parse_env $1 $2
    
    print_message "Accessing MySQL container ($ENV environment)..." "${BLUE}"
    dc exec mysql mysql -u root -p
}

access_redis() {
    parse_env $1 $2
    
    print_message "Accessing Redis container ($ENV environment)..." "${BLUE}"
    dc exec redis redis-cli
}

run_artisan() {
    print_message "Running artisan command: $*" "${BLUE}"
    dc exec -T app php artisan "$@"
}

run_composer() {
    print_message "Running composer command: $*" "${BLUE}"
    dc exec -T app composer "$@"
}

run_npm() {
    print_message "Running npm command: $*" "${BLUE}"
    dc exec -T app npm "$@"
}

run_migrations() {
    parse_env $1 $2
    
    print_message "Running database migrations ($ENV environment)..." "${BLUE}"
    if [ "$ENV" = "prod" ]; then
        dc exec -T app php artisan migrate --force
    else
        dc exec -T app php artisan migrate
    fi
    print_message "Migrations completed successfully!" "${GREEN}"
}

run_seeders() {
    parse_env $1 $2
    
    print_message "Running database seeders ($ENV environment)..." "${BLUE}"
    if [ "$ENV" = "prod" ]; then
        dc exec -T app php artisan db:seed --force
    else
        dc exec -T app php artisan db:seed
    fi
    print_message "Seeding completed successfully!" "${GREEN}"
}

fresh_migrate() {
    parse_env $1 $2
    
    print_message "Running fresh migration with seed ($ENV environment)..." "${YELLOW}"
    if [ "$ENV" = "prod" ]; then
        print_message "Warning: This will wipe all data in production!" "${RED}"
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_message "Operation cancelled." "${YELLOW}"
            exit 0
        fi
        dc exec -T app php artisan migrate:fresh --seed --force
    else
        dc exec -T app php artisan migrate:fresh --seed
    fi
    print_message "Fresh migration completed successfully!" "${GREEN}"
}

optimize_app() {
    parse_env $1 $2
    
    print_message "Optimizing Laravel application ($ENV environment)..." "${BLUE}"
    dc exec -T app php artisan optimize:clear
    dc exec -T app php artisan optimize
    
    if [ "$ENV" = "prod" ]; then
        dc exec -T app php artisan config:cache
        dc exec -T app php artisan route:cache
        dc exec -T app php artisan view:cache
    fi
    
    print_message "Optimization completed successfully!" "${GREEN}"
}

generate_ssl() {
    if [ "$ENV_TYPE" != "production" ]; then
        print_message "SSL generation is only available in production environment" "${RED}"
        exit 1
    fi
    
    local domain=$1
    
    if [ -z "$domain" ]; then
        domain="${NGINX_HOST:-$DEFAULT_DOMAIN}"
    fi

    print_message "Obtaining Let's Encrypt SSL certificate for $domain..." "${YELLOW}"
    
    # Set environment variables
    export NGINX_HOST="$domain"
    export LETSENCRYPT_EMAIL="${LETSENCRYPT_EMAIL:-<EMAIL>}"
    
    # Make sure nginx is running
    dc up -d nginx
    sleep 5
    
    # Generate SSL certificate
    print_message "Generating SSL certificate using certbot..." "${BLUE}"
    dc run --rm certbot certonly --webroot -w /var/www/certbot --email "$LETSENCRYPT_EMAIL" -d "$domain" --agree-tos --non-interactive
    
    if [ $? -eq 0 ]; then
        print_message "SSL certificate generated successfully!" "${GREEN}"
        print_message "Reloading nginx configuration..." "${BLUE}"
        dc exec nginx nginx -s reload
        print_message "Your site is now available at https://$domain" "${CYAN}"
    else
        print_message "Failed to generate SSL certificate" "${RED}"
        exit 1
    fi
}

show_status() {
    parse_env $1 $2
    
    print_message "Container status ($ENV environment):" "${BLUE}"
    dc ps
    
    print_message "\nDocker system information:" "${BLUE}"
    docker system df
}

clean_docker() {
    print_message "Cleaning up Docker system..." "${YELLOW}"
    
    # Stop all containers first
    dc down || true
    
    # Remove unused containers, networks, images, and volumes
    docker system prune -f
    docker volume prune -f
    
    print_message "Docker system cleaned up!" "${GREEN}"
}

full_install() {
    print_message "Starting full installation for $PROJECT_NAME ($ENV_TYPE environment)..." "${GREEN}"
    
    # Check prerequisites
    check_prerequisites
    
    # Create necessary directories
    print_message "Creating necessary directories..." "${BLUE}"
    mkdir -p ./storage/app/public
    mkdir -p ./storage/framework/{cache,sessions,views}
    mkdir -p ./storage/logs
    mkdir -p ./bootstrap/cache
    
    if [ "$ENV_TYPE" = "production" ]; then
        mkdir -p ./certbot/{conf,www}
    fi
    
    # Set permissions
    print_message "Setting permissions..." "${BLUE}"
    chmod -R 755 ./storage
    chmod -R 755 ./bootstrap/cache
    
    # Build and start services
    print_message "Building Docker images..." "${BLUE}"
    build_services prod
    
    print_message "Starting services..." "${BLUE}"
    start_services prod
    
    print_message "Installation completed successfully!" "${GREEN}"
    
    if [ "$ENV_TYPE" = "production" ]; then
        print_message "Your application is ready at: https://$DEFAULT_DOMAIN" "${CYAN}"
        print_message "To generate SSL certificate, run: $0 ssl $DEFAULT_DOMAIN" "${YELLOW}"
    else
        print_message "Your application is ready at: http://localhost:8000" "${CYAN}"
    fi
}

# Main script logic
case $1 in
    up|start)
        check_prerequisites
        start_services $2 $3
        ;;
    down|stop)
        check_prerequisites
        stop_services $2 $3
        ;;
    build)
        check_prerequisites
        build_services $2 $3
        ;;
    rebuild)
        check_prerequisites
        rebuild_services $2 $3
        ;;
    logs)
        check_prerequisites
        view_logs $2 $3
        ;;
    shell|bash)
        check_prerequisites
        access_shell $2 $3
        ;;
    mysql)
        check_prerequisites
        access_mysql $2 $3
        ;;
    redis)
        check_prerequisites
        access_redis $2 $3
        ;;
    artisan)
        check_prerequisites
        shift
        run_artisan "$@"
        ;;
    composer)
        check_prerequisites
        shift
        run_composer "$@"
        ;;
    npm)
        check_prerequisites
        shift
        run_npm "$@"
        ;;
    migrate)
        check_prerequisites
        run_migrations $2 $3
        ;;
    seed)
        check_prerequisites
        run_seeders $2 $3
        ;;
    fresh)
        check_prerequisites
        fresh_migrate $2 $3
        ;;
    optimize)
        check_prerequisites
        optimize_app $2 $3
        ;;
    ssl)
        check_prerequisites
        generate_ssl $2
        ;;
    status)
        check_prerequisites
        show_status $2 $3
        ;;
    clean)
        clean_docker
        ;;
    install)
        full_install
        ;;
    *)
        print_usage
        exit 1
        ;;
esac

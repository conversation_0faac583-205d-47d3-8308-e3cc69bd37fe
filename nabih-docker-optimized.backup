#!/bin/bash

# Nabih Docker Optimized - Unified Docker management script with improved CI/CD
set -e

# Global variables
PROJECT_NAME="nabih-api"
DEV_COMPOSE_FILE="docker-compose.yml"
PROD_COMPOSE_FILE="docker-compose.prod.optimized.yml"
ENV="dev"  # Default to development environment

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print a message with a color
print_message() {
    echo -e "${2}${1}${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_message "Docker is not running! Please start Docker and try again." "${RED}"
        exit 1
    fi
}

# Function to validate domain name
validate_domain() {
    if [ -z "$1" ]; then
        print_message "Domain name is required!" "${RED}"
        exit 1
    fi
}

# Function to display usage
show_usage() {
    print_message "Nabih Docker Optimized - Unified Docker management script" "${GREEN}"
    print_message "\nUsage: nabih-docker-optimized [environment] [command]" "${YELLOW}"
    print_message "\nEnvironments:" "${BLUE}"
    print_message "  dev (default) - Development environment" "${BLUE}"
    print_message "  prod          - Production environment" "${BLUE}"
    print_message "\nCommon Commands:" "${BLUE}"
    print_message "  start         - Start containers" "${BLUE}"
    print_message "  stop          - Stop containers" "${BLUE}"
    print_message "  restart       - Restart containers" "${BLUE}"
    print_message "  logs [container] - Show container logs" "${BLUE}"
    print_message "  bash [container] - Get shell access to a container" "${BLUE}"
    print_message "  artisan [cmd]  - Run Laravel Artisan commands" "${BLUE}"
    print_message "  rebuild       - Rebuild Docker images from scratch" "${BLUE}"
    print_message "  clean         - Clean Docker resources and project files" "${BLUE}"
    print_message "  cicd-cleanup  - Clean Docker system after CI/CD process" "${BLUE}"
    print_message "\nDevelopment-specific Commands:" "${BLUE}"
    print_message "  composer [cmd] - Run Composer commands" "${BLUE}"
    print_message "  npm [cmd]      - Run NPM commands" "${BLUE}"
    print_message "  clear-cache    - Clear Laravel cache" "${BLUE}"
    print_message "  pint [options] - Run Laravel Pint code formatter" "${BLUE}"
    print_message "\nProduction-specific Commands:" "${BLUE}"
    print_message "  deploy [--ci]  - Deploy production environment (requires NGINX_HOST)" "${BLUE}"
    print_message "  ssl [domain]   - Generate SSL certificate for domain" "${BLUE}"
    print_message "  complete-rebuild-and-deploy <host> - Delete everything and rebuild from scratch with specified host" "${BLUE}"
    print_message "  backup-db     - Backup the database" "${BLUE}"
    print_message "\nExamples:" "${GREEN}"
    print_message "  nabih-docker-optimized start                  - Start dev environment" "${GREEN}"
    print_message "  nabih-docker-optimized prod start             - Start production environment" "${GREEN}"
    print_message "  nabih-docker-optimized rebuild                - Rebuild Docker images from scratch" "${GREEN}"
    print_message "  NGINX_HOST=example.com nabih-docker-optimized prod deploy - Deploy to production" "${GREEN}"
    print_message "  nabih-docker-optimized prod ssl example.com   - Generate SSL certificate for domain" "${GREEN}"
}

# Get the compose file based on environment
get_compose_file() {
    if [ "$ENV" = "dev" ]; then
        echo $DEV_COMPOSE_FILE
    else
        echo $PROD_COMPOSE_FILE
    fi
}

# Start containers
start() {
    local compose_file=$(get_compose_file)

    if [ "$ENV" = "dev" ]; then
        print_message "Starting development environment..." "${GREEN}"
        docker compose -f $compose_file up -d

        # Sleep for 5 seconds to ensure containers are fully started
        print_message "Waiting for containers to initialize (5 seconds)..." "${BLUE}"
        sleep 5

        # Run migrations for dev environment
        print_message "Running database migrations..." "${BLUE}"
        docker compose -f $compose_file exec app php artisan migrate --force

        print_message "Development environment started! Access your application at http://localhost:8000" "${GREEN}"
    else
        print_message "Starting production environment..." "${GREEN}"
        docker compose -f $compose_file up -d

        # Sleep for 5 seconds to ensure containers are fully started
        print_message "Waiting for containers to initialize (5 seconds)..." "${BLUE}"
        sleep 5

        # Install dependencies if needed
        if [ ! -d "vendor" ] || [ ! -d "node_modules" ]; then
            print_message "Installing dependencies..." "${BLUE}"
            docker compose -f $compose_file exec app composer install --no-dev --optimize-autoloader --ignore-platform-req=ext-grpc
            docker compose -f $compose_file exec app npm install
            docker compose -f $compose_file exec app npm run build
        fi

        # Run migrations for prod environment
        print_message "Running database migrations..." "${BLUE}"
        docker compose -f $compose_file exec app php artisan migrate --force

        # Optimize application
        print_message "Optimizing application..." "${BLUE}"
        docker compose -f $compose_file exec app php artisan optimize

        print_message "Production environment started!" "${GREEN}"
        print_message "Access your application at https://${NGINX_HOST:-localhost}" "${GREEN}"
    fi
}

# Stop containers
stop() {
    local compose_file=$(get_compose_file)

    if [ "$ENV" = "dev" ]; then
        print_message "Stopping development environment..." "${YELLOW}"
    else
        print_message "Stopping production environment..." "${YELLOW}"
    fi

    docker compose -f $compose_file down
    print_message "Environment stopped!" "${GREEN}"
}

# Restart containers
restart() {
    local compose_file=$(get_compose_file)

    if [ "$ENV" = "dev" ]; then
        print_message "Restarting development environment..." "${YELLOW}"
    else
        print_message "Restarting production environment..." "${YELLOW}"
    fi

    docker compose -f $compose_file down
    docker compose -f $compose_file up -d
    print_message "Environment restarted!" "${GREEN}"
}

# Show logs
show_logs() {
    local compose_file=$(get_compose_file)
    local container="$1"

    if [ -z "$container" ]; then
        print_message "Showing logs for all containers..." "${YELLOW}"
        docker compose -f $compose_file logs -f
    else
        print_message "Showing logs for container: $container..." "${YELLOW}"
        docker compose -f $compose_file logs -f "$container"
    fi
}

# Execute Laravel artisan command
artisan() {
    local compose_file=$(get_compose_file)

    print_message "Running artisan command: $*" "${YELLOW}"

    # In production mode with specific commands, use a safer approach to avoid package issues
    if [ "$ENV" = "prod" ] && [[ "$*" == *"migrate"* || "$*" == *"config:cache"* || "$*" == *"route:cache"* || "$*" == *"view:cache"* ]]; then
        print_message "Using direct command execution to avoid provider issues..." "${BLUE}"
        docker compose -f $compose_file exec app php -d error_reporting=E_ALL^E_DEPRECATED artisan "$@"
    else
        docker compose -f $compose_file exec app php artisan "$@"
    fi
}

# Run composer (available in both dev and prod)
composer() {
    local compose_file=$(get_compose_file)

    print_message "Running composer command: $*" "${YELLOW}"
    docker compose -f $compose_file exec app composer "$@"
}

# Run npm (available in both dev and prod)
npm() {
    local compose_file=$(get_compose_file)

    print_message "Running npm command: $*" "${YELLOW}"
    docker compose -f $compose_file exec app npm "$@"
}

# Run bash in container
bash() {
    local compose_file=$(get_compose_file)
    local container="app"

    if [ ! -z "$1" ]; then
        container="$1"
    fi

    print_message "Starting bash session in $container container..." "${GREEN}"
    docker compose -f $compose_file exec $container bash
}

# Clear all caches (dev only)
clear_cache() {
    if [ "$ENV" = "prod" ]; then
        print_message "Clear cache command is only available in development environment" "${RED}"
        exit 1
    fi

    print_message "Clearing all Laravel caches..." "${YELLOW}"
    docker compose -f $DEV_COMPOSE_FILE exec app php artisan cache:clear
    docker compose -f $DEV_COMPOSE_FILE exec app php artisan config:clear
    docker compose -f $DEV_COMPOSE_FILE exec app php artisan route:clear
    docker compose -f $DEV_COMPOSE_FILE exec app php artisan view:clear
    docker compose -f $DEV_COMPOSE_FILE exec app php artisan optimize:clear
    print_message "All caches cleared!" "${GREEN}"
}

# Generate Let's Encrypt SSL certificate
generate_ssl() {
    local domain="$1"

    if [ "$ENV" != "prod" ]; then
        print_message "SSL certificate generation is only available in production environment" "${RED}"
        exit 1
    fi

    if [ -z "$domain" ]; then
        domain="${NGINX_HOST:-dev.nabih.sa}"
    fi

    print_message "Obtaining Let's Encrypt SSL certificate for $domain..." "${YELLOW}"

    # Create certbot directories
    mkdir -p ./certbot/conf
    mkdir -p ./certbot/www

    # Set environment variables for certbot
    export NGINX_HOST="$domain"
    export LETSENCRYPT_EMAIL="${LETSENCRYPT_EMAIL:-<EMAIL>}"

    # Start nginx first to handle ACME challenges
    local compose_file=$(get_compose_file)
    docker compose -f $compose_file up -d nginx

    # Wait for nginx to be ready
    sleep 5

    # Obtain certificate using certbot
    if docker compose -f $compose_file run --rm certbot; then
        print_message "Let's Encrypt SSL certificate obtained successfully for $domain!" "${GREEN}"
        
        # Reload nginx to use the new certificate
        docker compose -f $compose_file exec nginx nginx -s reload
        print_message "NGINX reloaded with new certificate" "${GREEN}"
    else
        print_message "Failed to obtain SSL certificate for $domain" "${RED}"
        print_message "Make sure the domain points to this server and port 80 is accessible" "${YELLOW}"
        exit 1
    fi
}

# Deploy production environment
deploy() {
    local ci_mode=false

    # Check if --ci flag is provided
    if [ "$1" = "--ci" ]; then
        ci_mode=true
        print_message "Running in CI/CD mode" "${BLUE}"
    fi

    if [ "$ENV" != "prod" ]; then
        print_message "Deploy command is only available in production environment" "${RED}"
        exit 1
    fi

    # Check if host is set
    if [ -z "$NGINX_HOST" ]; then
        print_message "Please set the NGINX_HOST environment variable:" "${RED}"
        print_message "Example: NGINX_HOST=example.com ./nabih-docker-optimized prod deploy" "${YELLOW}"
        exit 1
    fi

    print_message "Deploying production environment for $NGINX_HOST..." "${GREEN}"

    # Set NGINX_HOST for both domain and IP address
    export NGINX_HOST="$NGINX_HOST"

    # Check if Let's Encrypt certificates exist, generate if not
    if [ ! -f "./certbot/conf/live/$NGINX_HOST/fullchain.pem" ] || [ ! -f "./certbot/conf/live/$NGINX_HOST/privkey.pem" ]; then
        print_message "Let's Encrypt certificates not found, obtaining..." "${BLUE}"
        generate_ssl "$NGINX_HOST"
    fi

    # Check if containers are already running
    if docker compose -f $PROD_COMPOSE_FILE ps | grep -q "nabih-app"; then
        print_message "Containers already running, performing incremental update..." "${BLUE}"

        # Install composer dependencies
        print_message "Updating Composer dependencies..." "${BLUE}"
        docker compose -f $PROD_COMPOSE_FILE exec app composer install --no-dev --optimize-autoloader --ignore-platform-req=ext-grpc

        # Install npm dependencies and build assets
        print_message "Updating NPM dependencies and building assets..." "${BLUE}"
        docker compose -f $PROD_COMPOSE_FILE exec app npm install
        docker compose -f $PROD_COMPOSE_FILE exec app npm run build

        # Run migrations
        print_message "Running database migrations..." "${BLUE}"
        docker compose -f $PROD_COMPOSE_FILE exec app php artisan migrate --force

        # Clear cache and optimize application
        print_message "Optimizing application..." "${BLUE}"
        docker compose -f $PROD_COMPOSE_FILE exec app php artisan optimize

        # Restart services to apply changes
        print_message "Restarting services..." "${BLUE}"
        docker compose -f $PROD_COMPOSE_FILE restart
    else
        # Use the start function to start containers
        start
    fi

    print_message "Deployment completed successfully!" "${GREEN}"
    print_message "Your application is now running at https://$NGINX_HOST" "${GREEN}"

    # Perform post-deployment cleanup if in CI mode
    if [ "$ci_mode" = true ]; then
        cicd_cleanup
    fi
}

# Backup database (prod only)
backup_db() {
    if [ "$ENV" != "prod" ]; then
        print_message "Database backup command is only available in production environment" "${RED}"
        exit 1
    fi

    print_message "Creating database backup..." "${YELLOW}"

    # Create backup directory if it doesn't exist
    mkdir -p ./backups

    # Generate timestamp
    TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
    BACKUP_FILE="./backups/database-$TIMESTAMP.sql"

    # Export database
    docker compose -f $PROD_COMPOSE_FILE exec -T mysql sh -c 'exec mysqldump -uroot -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"' > $BACKUP_FILE

    print_message "Database backup created: $BACKUP_FILE" "${GREEN}"
}

# Generate app key for Laravel
generate_key() {
    local compose_file=$(get_compose_file)

    if [ "$ENV" = "dev" ]; then
        print_message "Generating application key in development environment..." "${YELLOW}"
        docker compose -f $compose_file exec app php artisan key:generate
    else
        print_message "Generating application key in production environment..." "${YELLOW}"
        # Generate key locally and update .env file directly
        local key=$(docker compose -f $compose_file exec app php -r "echo 'base64:' . base64_encode(random_bytes(32));")

        if [ -f ".env" ]; then
            # Check if APP_KEY already exists
            if grep -q "APP_KEY=" .env; then
                # Replace existing APP_KEY
                print_message "Updating existing APP_KEY in .env file..." "${BLUE}"
                sed -i "s|APP_KEY=.*|APP_KEY=$key|" .env
            else
                # Add APP_KEY if it doesn't exist
                print_message "Adding APP_KEY to .env file..." "${BLUE}"
                echo "APP_KEY=$key" >> .env
            fi
            print_message "Application key set successfully: $key" "${GREEN}"
        else
            print_message "Error: .env file not found" "${RED}"
            exit 1
        fi
    fi
}

# Rebuild images from scratch
rebuild() {
    local compose_file=$(get_compose_file)

    if [ "$ENV" = "dev" ]; then
        print_message "Rebuilding development environment images..." "${YELLOW}"
    else
        print_message "Rebuilding production environment images..." "${YELLOW}"
    fi

    # Stop any running containers
    print_message "Stopping containers..." "${BLUE}"
    docker compose -f $compose_file down

    # Remove any existing images for this project
    print_message "Removing existing images..." "${BLUE}"
    docker compose -f $compose_file down --rmi local

    # Build images without starting containers
    print_message "Building fresh images..." "${BLUE}"
    docker compose -f $compose_file build --no-cache

    # Start containers
    print_message "Starting containers with new images..." "${BLUE}"
    docker compose -f $compose_file up -d

    print_message "Rebuild completed successfully!" "${GREEN}"
}

# Clean Docker resources and project files
clean() {
    local compose_file=$(get_compose_file)

    print_message "Cleaning up Docker resources and project files..." "${YELLOW}"

    # Stop containers if running
    print_message "Stopping containers if running..." "${BLUE}"
    docker compose -f $compose_file down 2>/dev/null || true

    # Remove Docker build cache
    print_message "Removing Docker build cache..." "${BLUE}"
    docker builder prune -f

    # Remove unused images
    print_message "Removing unused Docker images..." "${BLUE}"
    docker image prune -f

    # Clean Laravel cache files
    if [ "$ENV" = "dev" ]; then
        print_message "Cleaning Laravel cache files..." "${BLUE}"
        rm -rf ./bootstrap/cache/*.php 2>/dev/null || true
        rm -rf ./storage/framework/cache/data/* 2>/dev/null || true
        rm -rf ./storage/framework/views/*.php 2>/dev/null || true
        rm -rf ./storage/logs/*.log 2>/dev/null || true
        rm -rf ./storage/framework/sessions/* 2>/dev/null || true
    fi

    print_message "Cleanup completed successfully!" "${GREEN}"
}

# Complete rebuild and deploy with specified host
complete_rebuild_and_deploy() {
    local host="$1"

    if [ "$ENV" != "prod" ]; then
        print_message "Complete rebuild and deploy should only be used with production environment!" "${RED}"
        print_message "Usage: nabih-docker-optimized prod complete-rebuild-and-deploy example.com" "${YELLOW}"
        exit 1
    fi

    if [ -z "$host" ]; then
        print_message "Host parameter is required!" "${RED}"
        print_message "Usage: nabih-docker-optimized prod complete-rebuild-and-deploy example.com" "${YELLOW}"
        exit 1
    fi

    print_message "===== STARTING COMPLETE REBUILD AND DEPLOYMENT WITH HOST: $host ====" "${GREEN}"

    # Set the host for deployment
    export NGINX_HOST="$host"
    print_message "Setting NGINX_HOST to $NGINX_HOST" "${GREEN}"

    # Clean Docker resources
    print_message "===== Cleaning Docker resources =====" "${BLUE}"
    clean

    # Remove additional Docker resources specific to production
    print_message "===== Removing additional Docker resources =====" "${BLUE}"
    docker volume rm $(docker volume ls -q | grep nabih) 2>/dev/null || echo "No volumes to remove"
    docker network rm nabih-prod-network 2>/dev/null || echo "Network already removed"

    # Generate SSL certificate
    print_message "===== Generating SSL certificate =====" "${BLUE}"
    generate_ssl "$host"

    # Rebuild from scratch
    print_message "===== Rebuilding from scratch =====" "${BLUE}"
    rebuild

    # Deploy with the specified host
    print_message "===== Deploying with host: $host =====" "${BLUE}"
    deploy

    print_message "===== Complete rebuild and deploy finished =====" "${GREEN}"
    print_message "Your application is now running at https://$host" "${GREEN}"
}

# Run Laravel Pint for code styling
run_pint() {
    local compose_file=$(get_compose_file)

    print_message "Running Laravel Pint code formatter..." "${BLUE}"

    # Check if we're in dev or prod environment
    if [ "$ENV" = "dev" ]; then
        # In development, run pint through Docker
        docker compose -f $compose_file exec app bash -c "if [ ! -f ./vendor/bin/pint ]; then composer require laravel/pint --dev; fi && ./vendor/bin/pint $*"
    else
        # In production, use Docker to run pint
        docker compose -f $compose_file exec app bash -c "if [ ! -f ./vendor/bin/pint ]; then composer require laravel/pint --dev; fi && ./vendor/bin/pint $*"
    fi

    print_message "Code formatting complete!" "${GREEN}"
}

# Clean system after CI/CD process completes
cicd_cleanup() {
    print_message "Performing post CI/CD cleanup..." "${BLUE}"

    # Clean up Docker system
    print_message "Cleaning Docker system (removing old images, containers, and build files)..." "${YELLOW}"
    docker system prune -f

    print_message "CI/CD cleanup completed successfully!" "${GREEN}"
}

# Check environment status
env_status() {
    local compose_file=$(get_compose_file)

    print_message "Checking environment status..." "${YELLOW}"
    print_message "Container .env file:" "${BLUE}"
    docker compose -f $compose_file exec app bash -c "ls -la .env || echo '.env file NOT FOUND!'"
    docker compose -f $compose_file exec app bash -c "cat .env 2>/dev/null | grep -v DB_PASSWORD || echo 'Could not read .env content'"

    print_message "\nContainer environment variables:" "${BLUE}"
    docker compose -f $compose_file exec app php artisan env
}

# Parse environment and command

# Check Docker status first
check_docker

# Determine the environment and command
CMD=""

if [ $# -eq 0 ]; then
    show_usage
    exit 0
fi

# Parse arguments - check if first arg is explicitly an environment
if [ "$1" = "dev" ] || [ "$1" = "prod" ]; then
    ENV="$1"
    shift
    if [ $# -eq 0 ]; then
        show_usage
        exit 0
    fi
    CMD="$1"
else
    # First argument is the command
    CMD="$1"
fi

# Process command
case "$CMD" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        show_logs "$2"
        ;;
    artisan)
        shift
        artisan "$@"
        ;;
    cicd-cleanup)
        cicd_cleanup
        ;;
    composer)
        shift
        composer "$@"
        ;;
    npm)
        shift
        npm "$@"
        ;;
    bash)
        bash "$2"
        ;;
    clear-cache)
        clear_cache
        ;;
    rebuild)
        rebuild
        ;;
    clean)
        clean
        ;;
    deploy)
        deploy "$2"
        ;;
    ssl)
        generate_ssl "$2"
        ;;
    complete-rebuild-and-deploy)
        complete_rebuild_and_deploy "$2"
        ;;
    pint)
        shift
        run_pint "$@"
        ;;
    backup-db)
        backup_db
        ;;
    env-status)
        env_status
        ;;
    key:generate)
        generate_key
        ;;
    *)
        print_message "Unknown command: $1" "${RED}"
        exit 1
        ;;
esac

exit 0

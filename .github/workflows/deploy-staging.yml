name: Deploy to Staging Server

on:
  push:
    branches: [ development ]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Staging (dev.nabih.sa)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}

      - name: Add SSH Known Hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.STAGING_EC2_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to Staging Server
        run: |
          # Create staging deployment script
          cat > deploy_staging.sh << 'EOF'
          #!/bin/bash
          set -e

          # Configuration for STAGING
          APP_DIR="${{ vars.STAGING_APP_DIR || '/var/www/nabih-api' }}"
          HOST="${{ vars.STAGING_HOST || 'dev.nabih.sa' }}"
          BRANCH="${{ vars.STAGING_BRANCH || 'development' }}"

          echo "Deploying to STAGING: $APP_DIR with host $HOST"

          # Navigate to application directory
          cd $APP_DIR

          # Pull latest code from development branch
          echo "Pulling latest code from $BRANCH branch..."
          git reset --hard HEAD
          git pull origin $BRANCH

          # Make scripts executable
          chmod +x ./nabih-docker-optimized
          chmod +x ./nabih-docker

          # Set environment variables for staging
          export NGINX_HOST="$HOST"
          export LETSENCRYPT_EMAIL="${{ vars.STAGING_LETSENCRYPT_EMAIL || '<EMAIL>' }}"
          DOCKER_COMPOSE_FILE="${{ vars.STAGING_DOCKER_COMPOSE_FILE || 'docker-compose.prod.optimized.yml' }}"
          CONTAINER_NAME="${{ vars.STAGING_CONTAINER_NAME || 'nabih-app-dev' }}"

          echo "Starting staging deployment..."

          # Check for build file changes that require container rebuild
          BUILD_FILES_CHANGED=false
          if git diff --name-only HEAD@{1} HEAD | grep -E "(Dockerfile|docker-compose|docker/)"; then
            echo "Build files changed, full rebuild required..."
            BUILD_FILES_CHANGED=true
          fi

          # Check if containers are already running and no rebuild needed
          if docker compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME" && [ "$BUILD_FILES_CHANGED" = "false" ]; then
            echo "Containers running, performing incremental update..."

            # Check for dependency changes
            COMPOSER_CHANGED=false
            PACKAGE_CHANGED=false
            MIGRATION_CHANGED=false

            if git diff --name-only HEAD@{1} HEAD | grep -q "composer.json\|composer.lock"; then
              echo "Composer dependencies changed, updating..."
              docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app composer install ${{ vars.STAGING_COMPOSER_FLAGS || '--optimize-autoloader --no-interaction --prefer-dist' }}
              COMPOSER_CHANGED=true
            fi

            if git diff --name-only HEAD@{1} HEAD | grep -q "package.json\|package-lock.json"; then
              echo "NPM dependencies changed, updating..."
              docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app npm install
              docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app npm run build
              PACKAGE_CHANGED=true
            fi

            # Check for new migration files
            if git diff --name-only HEAD@{1} HEAD | grep -q "database/migrations/"; then
              echo "New migration files detected..."
              MIGRATION_CHANGED=true
            fi

            # Run migrations
            echo "Running migrations..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan migrate --force

            # Clear cache and optimize
            echo "Optimizing application..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan optimize:clear
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan optimize

            # Restart queue workers if dependencies or migrations changed
            if [ "$COMPOSER_CHANGED" = "true" ] || [ "$MIGRATION_CHANGED" = "true" ]; then
              echo "Restarting queue workers due to dependency/migration changes..."
              docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app supervisorctl restart queue:* 2>/dev/null || true
              docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app supervisorctl restart scheduler:* 2>/dev/null || true
            fi

            # Restart PHP-FPM to ensure code changes are loaded
            echo "Restarting PHP-FPM..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app supervisorctl restart php-fpm 2>/dev/null || true

            # Final restart to ensure all changes are loaded
            echo "Performing final application restart..."
            docker compose -f "$DOCKER_COMPOSE_FILE" restart app
          else
            echo "Performing fresh deployment..."

            # Stop any existing containers
            docker compose -f "$DOCKER_COMPOSE_FILE" down || true

            # Build fresh images
            echo "Building fresh Docker images..."
            docker compose -f "$DOCKER_COMPOSE_FILE" build --no-cache

            # Start containers
            docker compose -f "$DOCKER_COMPOSE_FILE" up -d

            # Wait for containers to initialize
            echo "Waiting for containers to initialize (${{ vars.STAGING_INIT_WAIT_TIME || '15' }} seconds)..."
            sleep ${{ vars.STAGING_INIT_WAIT_TIME || '15' }}

            # Install dependencies with configurable flags
            echo "Installing dependencies..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app composer install ${{ vars.STAGING_COMPOSER_FLAGS || '--optimize-autoloader --no-interaction --prefer-dist' }}
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app npm install
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app npm run build

            # Run migrations
            echo "Running migrations..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan migrate --force

            # Clear cache and optimize
            echo "Optimizing application..."
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan optimize:clear
            docker compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan optimize

            # Final restart to ensure all optimizations are loaded
            echo "Performing final application restart..."
            docker compose -f "$DOCKER_COMPOSE_FILE" restart app
          fi

          echo "STAGING deployment completed successfully!"
          echo "Your application is now running at https://$HOST"

          # Clean up Docker system
          echo "Cleaning up Docker system..."
          docker system prune -f
          EOF

          chmod +x deploy_staging.sh
          scp deploy_staging.sh ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_EC2_HOST }}:/tmp/deploy_staging.sh
          ssh ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_EC2_HOST }} "bash /tmp/deploy_staging.sh && rm /tmp/deploy_staging.sh"
          rm deploy_staging.sh

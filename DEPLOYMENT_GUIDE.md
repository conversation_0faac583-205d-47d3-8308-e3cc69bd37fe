# Nabih API - Dual Environment Deployment Guide

This guide explains the new dual-environment CI/CD setup for staging and production deployments.

## 🏗️ Architecture Overview

### Staging Environment
- **Branch**: `development`
- **Domain**: `dev.api.nabih.sa`
- **Server Path**: `/var/www/nabih-api`
- **Workflow**: `.github/workflows/deploy-staging.yml`

### Production Environment
- **Branch**: `main` 
- **Domain**: `api.nabih.sa`
- **Server Path**: `/var/www/nabih-prod`
- **Workflow**: `.github/workflows/deploy-ec2-optimized.yml`

## 🚀 Automatic Deployments

### Staging Deployment
Triggers automatically when code is pushed to the `development` branch:
```bash
git push origin development
```

### Production Deployment  
Triggers automatically when code is pushed to the `main` branch:
```bash
git push origin main
```

## 🔧 Manual Management

### Using the Enhanced Docker Script

The `nabih-docker-optimized` script now auto-detects the environment based on the current directory:

```bash
# In /var/www/nabih-api (staging)
./nabih-docker-optimized up prod    # Uses dev.api.nabih.sa configuration

# In /var/www/nabih-prod (production)  
./nabih-docker-optimized up prod    # Uses api.nabih.sa configuration
```

### SSL Certificate Management

Use the new SSL setup script:

```bash
# Generate SSL for staging only
./ssl-setup.sh staging

# Generate SSL for production only
./ssl-setup.sh production

# Generate SSL for both environments
./ssl-setup.sh both
```

## 📁 Directory Structure

```
Server Structure:
├── /var/www/nabih-api/          # Staging environment
│   ├── .git (development branch)
│   └── docker-compose.prod.optimized.yml
└── /var/www/nabih-prod/         # Production environment  
    ├── .git (main branch)
    └── docker-compose.prod.optimized.yml
```

## 🔐 SSL Certificates

Both environments support SSL certificates:

- **Staging**: `dev.api.nabih.sa` 
- **Production**: `api.nabih.sa`

Certificates are stored in:
```
./certbot/conf/live/dev.api.nabih.sa/    # Staging SSL
./certbot/conf/live/api.nabih.sa/        # Production SSL
```

## 🔄 Workflow Behaviors

### Staging Workflow (`deploy-staging.yml`)
- Triggers on `development` branch pushes
- Deploys to `/var/www/nabih-api`
- Uses `dev.api.nabih.sa` domain configuration
- Runs incremental updates when possible

### Production Workflow (`deploy-ec2-optimized.yml`)  
- Triggers on `main` branch pushes
- Deploys to `/var/www/nabih-prod`
- Uses `api.nabih.sa` domain configuration
- Optimized for production performance

## 🛠️ Container Management

Both environments use the same container structure but with environment-specific configurations:

- **App Container**: `nabih-app`
- **Nginx Container**: `nabih-nginx` 
- **MySQL Container**: `nabih-mysql`
- **Redis Container**: `nabih-redis`
- **PhpMyAdmin Container**: `nabih-phpmyadmin`
- **Certbot Container**: `nabih-certbot`

## ⚙️ Environment Variables

Key environment variables that differentiate the environments:

```bash
# Staging
NGINX_HOST=dev.api.nabih.sa
LETSENCRYPT_EMAIL=<EMAIL>

# Production  
NGINX_HOST=api.nabih.sa
LETSENCRYPT_EMAIL=<EMAIL>
```

## 🚨 Important Notes

1. **Container Names**: Both environments use the same container names but different network configurations
2. **SSL Management**: Each environment manages its own SSL certificates  
3. **Auto-Detection**: Scripts automatically detect environment based on current directory
4. **Branch Separation**: Staging uses `development` branch, Production uses `main` branch

## 🔍 Monitoring & Logs

View logs for either environment:

```bash
# Staging (from /var/www/nabih-api)
./nabih-docker-optimized logs prod

# Production (from /var/www/nabih-prod)  
./nabih-docker-optimized logs prod
```

## 🆘 Troubleshooting

### Check Container Status
```bash
./nabih-docker-optimized status prod
```

### Rebuild if Issues
```bash  
./nabih-docker-optimized rebuild prod
```

### Manual SSL Generation
```bash
./ssl-setup.sh [staging|production|both]
```

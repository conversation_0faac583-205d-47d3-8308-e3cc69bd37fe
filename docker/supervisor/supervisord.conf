[supervisord]
nodaemon=true
user=root

[include]
files = /etc/supervisor/conf.d/*.conf

[program:php-fpm]
command=/usr/local/sbin/php-fpm
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=0
user=root

[program:queue]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work --tries=3 --timeout=90
user=www-data
autostart=true
autorestart=true
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/worker.log
stopwaitsecs=3600

[program:scheduler]
process_name=%(program_name)s_%(process_num)02d
command=/bin/sh -c "while [ true ]; do (php /var/www/artisan schedule:run --verbose --no-interaction &); sleep 60; done"
user=www-data
autostart=true
autorestart=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/scheduler.log

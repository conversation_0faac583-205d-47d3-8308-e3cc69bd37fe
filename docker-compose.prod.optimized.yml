services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod.optimized
    image: nabih-api-app:latest
    container_name: nabih-app
    restart: unless-stopped
    working_dir: /var/www
    # Mount the entire codebase as a volume for easier updates
    volumes:
      - .:/var/www:cached
      - ./docker/php/php.prod.ini:/usr/local/etc/php/conf.d/99-overrides.ini:ro
      - vendor:/var/www/vendor # Use a named volume for vendor to preserve it between rebuilds
      - node_modules:/var/www/node_modules # Use a named volume for node_modules to preserve it between rebuilds
    environment:
      - "DB_HOST=database-1.c700o0q8ygwy.me-south-1.rds.amazonaws.com"
      - "REDIS_HOST=redis"
      - "QUEUE_CONNECTION=redis"
      - "SESSION_DRIVER=redis"
      - "CACHE_DRIVER=redis"
    depends_on:
      - mysql
      - redis
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: nabih-nginx
    restart: unless-stopped
    ports:
      - "80:80"     # Standard HTTP port
      - "443:443"   # Standard HTTPS port
    volumes:
      - ./public:/var/www/public:ro
      - ./docker/nginx/conf.d/app.prod.conf.template:/etc/nginx/templates/default.conf.template:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - ./certbot/www:/var/www/certbot:ro
    environment:
      - NGINX_HOST=${NGINX_HOST:-localhost}
    depends_on:
      - app
    networks:
      - app-network

  certbot:
    image: certbot/certbot
    container_name: nabih-certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    environment:
      - NGINX_HOST=${NGINX_HOST:-dev.nabih.sa}
      - LETSENCRYPT_EMAIL=${LETSENCRYPT_EMAIL:-<EMAIL>}
    # This service will be run manually for certificate generation
    # Use: docker-compose run --rm certbot certonly --webroot -w /var/www/certbot --email <EMAIL> -d domain.com --agree-tos --non-interactive
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew --webroot -w /var/www/certbot --quiet; sleep 12h & wait $${!}; done;'"
    depends_on:
      - nginx
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    container_name: nabih-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-laravel}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_USER: ${DB_USERNAME:-laravel}
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    networks:
      - app-network

  redis:
    image: redis:latest
    container_name: nabih-redis
    restart: unless-stopped
    volumes:
      - redisdata:/data
    command: redis-server --appendonly yes
    networks:
      - app-network
      
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: nabih-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-secret}
    depends_on:
      - mysql
    networks:
      - app-network

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local
  vendor:
    driver: local
  node_modules:
    driver: local

networks:
  app-network:
    driver: bridge
    name: nabih-prod-network
    ipam:
      config:
        - subnet: **********/16
